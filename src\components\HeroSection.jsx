import { getTranslations } from "next-intl/server";
import SearchForm from "./SearchForm";
import { useTranslations } from "next-intl";
import { Award, AwardIcon, Star, Users } from "lucide-react";

export default async function HeroSection() {
  const t = await getTranslations("hero");
  return (
    <div
      className="relative min-h-[95dvh] bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/img/heroImg.webp')",
      }}
    >
      {/* Dark Overlay */}
      <div className="absolute inset-0 w-full h-full bg-gray-700/20 bg-opacity-20 z-5"></div>
      {/* white Overlay */}
      <div className="absolute -bottom-[1px] w-full h-[10dvh] bg-gradient-to-b from-white/0 via-white/50 to-gray-50 bg-opacity-60 z-10"></div>

      {/* Hero Content */}
      <div className="relative z-20 flex items-center justify-center pt-16 min-h-[105dvh]">
        <div className="max-w-7xl h-full flex flex-col justify-around items-center mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
          <div className="text-center flex justify-center items-center flex-col gap-2">
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
              <span className="text-white/90 text-sm font-medium">
                {" "}
                {t("experienceLiving")}{" "}
              </span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold md:font-extrabold text-white mb-6">
              {t("welcome")}
            </h1>

            <p className="text-xl text-gray-100 mb-8 max-w-3xl mx-auto">
              {t("subtitle")}
            </p>
            <div className="flex items-center justify-center space-x-8 mb-8 text-white/80">
              <div className="flex items-center space-x-2">
                <Star />
                <span className="text-sm font-medium">{t("rating")} 4.9</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users />
                <span className="text-sm font-medium">{t("happyGuests")}</span>
              </div>
              <div className="hidden md:flex items-center space-x-2 ">
                <Award />
                <span className="text-sm font-medium">{t("awardWinning")}</span>
              </div>
            </div>
          </div>
          <SearchForm />
        </div>
      </div>
    </div>
  );
}
