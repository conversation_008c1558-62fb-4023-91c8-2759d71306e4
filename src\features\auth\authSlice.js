import { createSlice, createAsyncThunk } from "@reduxjs/toolkit"
import * as authService from "./authService"
import { jwtDecode } from "jwt-decode"

// login
export const login = createAsyncThunk(
  "auth/login",
  async (userData, thunkAPI) => {
    try {
      return await authService.login(userData)
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message)
    }
  }
)

// register
export const register = createAsyncThunk(
  "auth/register",
  async (userData, thunkAPI) => {
    try {
      return await authService.register(userData)
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message)
    }
  }
)

// refresh token
export const refreshToken = createAsyncThunk(
  "auth/refresh",
  async (_, thunkAPI) => {
    try {
      return await authService.refresh()
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message)
    }
  }
)

const initialState = {
  user: null,
  token: null,
  refreshToken: null,
  expiresIn: false,
  refreshTokenExpiration: null,
  isLoading: false,
  isError: null,
  role: null,
  permissions: [],
}

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null
      state.token = null
      state.refreshToken = null
      state.expiresIn = null
      state.refreshTokenExpiration = null
      state.role = null
      state.permissions = []
      localStorage.removeItem("token")
      localStorage.removeItem("refreshToken")
      localStorage.removeItem("user")
    },
    loadUserFromStorage: (state) => {
      const token = localStorage.getItem("token")
      const refreshToken = localStorage.getItem("refreshToken")
      const user = localStorage.getItem("user")
      if (token && refreshToken && user) {
        state.token = token
        state.refreshToken = refreshToken
        state.user = JSON.parse(user)

        const decoded = jwtDecode(token)
        state.role = decoded.roles?.[0] || null
        state.permissions = decoded.permissions || []
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // login
      .addCase(login.pending, (state) => {
        state.isLoading = true
        state.isError = null
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false
        const { id, email, firstName, lastName, token, refreshToken, expiresIn, refreshTokenExpiration } =
          action.payload

        const decoded = jwtDecode(token)

        state.user = { id, email, firstName, lastName }
        state.token = token
        state.refreshToken = refreshToken
        state.expiresIn = expiresIn
        state.refreshTokenExpiration = refreshTokenExpiration
        state.role = decoded.roles?.[0] || null
        state.permissions = decoded.permissions || []

        localStorage.setItem("token", token)
        localStorage.setItem("refreshToken", refreshToken)
        localStorage.setItem("user", JSON.stringify({ id, email, firstName, lastName }))
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false
        state.isError = action.payload
      })

      // register
      .addCase(register.pending, (state) => {
        state.isLoading = true
        state.isError = null
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false
        const { id, email, firstName, lastName, token, refreshToken, expiresIn, refreshTokenExpiration } =
          action.payload

        const decoded = jwtDecode(token)

        state.user = { id, email, firstName, lastName }
        state.token = token
        state.refreshToken = refreshToken
        state.expiresIn = expiresIn
        state.refreshTokenExpiration = refreshTokenExpiration
        state.role = decoded.roles?.[0] || null
        state.permissions = decoded.permissions || []

        localStorage.setItem("token", token)
        localStorage.setItem("refreshToken", refreshToken)
        localStorage.setItem("user", JSON.stringify({ id, email, firstName, lastName }))
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false
        state.isError = action.payload
      })

      // refresh token
      .addCase(refreshToken.fulfilled, (state, action) => {
        const { token, expiresIn } = action.payload
        state.token = token
        state.expiresIn = expiresIn
        localStorage.setItem("token", token)

        const decoded = jwtDecode(token)
        state.role = decoded.roles?.[0] || null
        state.permissions = decoded.permissions || []
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isError = action.payload
      })
  },
})

export const { logout, loadUserFromStorage } = authSlice.actions
export default authSlice.reducer
