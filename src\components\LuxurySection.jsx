"use client"
import { useState } from "react";
import { Check, Play, X } from "lucide-react";

export default function LuxurySection() {
  const [isOpen, setIsOpen] = useState(false);

  // Static features in English
  const features = [
    "Spacious and modern rooms",
    "Panoramic city views",
    "Private swimming pool",
    "World-class dining experience",
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-slate-800 mb-4">
                Luxury Redefined
              </h2>
              <h3 className="text-2xl md:text-3xl font-semibold text-primary mb-6">
                Experience True Comfort
              </h3>
              <p className="text-lg text-slate-600 leading-relaxed">
                Discover the perfect blend of elegance and comfort, crafted to make your stay truly unforgettable.
              </p>
            </div>

            {/* Features */}
            <div className="space-y-4">
              {features.map((feature, i) => (
                <div
                  key={i}
                  className="flex items-center gap-2 space-x-reverse space-x-3"
                >
                  <div className="w-6 h-6 rounded-full flex items-center bg-primary justify-center flex-shrink-0 shadow-sm">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-slate-700 font-medium">{feature}</span>
                </div>
              ))}
            </div>

            {/* Video button */}
            <button
              onClick={() => setIsOpen(true)}
              className="group cursor-pointer inline-flex items-center gap-2 bg-slate-800 hover:bg-slate-700 text-white px-6 py-3 rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 space-x-reverse space-x-3"
            >
              <span className="font-medium text-sm">Watch Video</span>
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center ml-2">
                <Play className="w-3 h-3 text-slate-800 group-hover:scale-110 transition-transform duration-300" />
              </div>
            </button>
          </div>

          {/* Image with overlay */}
          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <img
                alt="Luxury Suite"
                className="w-full h-96 lg:h-[500px] object-cover"
                src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              />
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                <button
                  onClick={() => setIsOpen(true)}
                  className="group cursor-pointer w-20 h-20 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl hover:bg-white hover:scale-110 transition-all duration-300 border border-white/20"
                >
                  <Play className="w-8 h-8 text-slate-700 ml-1 group-hover:scale-110 transition-transform duration-300" />
                </button>
              </div>
            </div>
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-400/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-slate-400/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Video modal */}
      {isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
          onClick={() => setIsOpen(false)}
        >
          <div
            className="relative bg-black rounded-2xl shadow-2xl 
              w-full 
              max-w-md sm:max-w-lg md:max-w-2xl lg:max-w-4xl 
              h-[60vh] sm:h-[70vh] md:h-[75vh] lg:h-[80vh]"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setIsOpen(false)}
              className="absolute -top-10 right-0 text-white hover:text-red-400 transition"
            >
              <X size={32} className="text-gray-400 hover:text-primary" />
            </button>
            <iframe
              className="w-full h-full rounded-2xl"
              src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"
              title="Luxury Video"
              frameBorder="0"
              allow="autoplay; fullscreen"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      )}
    </section>
  );
}
