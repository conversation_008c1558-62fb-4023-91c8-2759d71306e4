import { store } from "../store/store";
import { refreshToken } from "../features/auth/authSlice";

export async function fetchWithAuth(url, options = {}) {
  let token = localStorage.getItem("token");

  let res = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      Authorization: `Bear<PERSON> ${token}`,
    },
  });

  if (res.status === 401) {
    // refresh
    await store.dispatch(refreshToken());
    token = localStorage.getItem("token");

    res = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
      },
    });
  }

  return res.json();
}
