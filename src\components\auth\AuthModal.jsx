import { useState, useEffect } from "react"
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogOverlay,
} from "@/components/ui/dialog"
import SignInForm from "./SignInForm"
import SignUpForm from "./SignUpForm"
import { useTranslations } from "next-intl"

export default function AuthModal({ open, onClose, initialMode = "signin",setAuthMode }) {
  const [mode, setMode] = useState(initialMode)
  const t = useTranslations("auth")

  // Sync mode when initialMode changes
  useEffect(() => {
    setMode(initialMode)
  }, [initialMode])


  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogOverlay className="backdrop-blur-sm bg-black/40" />
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-col justify-center items-center">
          <DialogTitle>{mode === "signin" ? "Sign In" : "Sign Up"}</DialogTitle>
          <DialogDescription >
            {mode === "signin"
              ? "Welcome Back"
              : "Welcome Back"
              }
          </DialogDescription>
        </DialogHeader>

        {mode === "signin" ? (
          <SignInForm setAuthMode={setAuthMode} onClose={onClose}  />
        ) : (
          <SignUpForm setAuthMode={setAuthMode} onClose={onClose}  />
        )}
      </DialogContent>
    </Dialog>
  )
}