import { create } from "zustand";
import api from "@/src/lib/axios";
import { jwtDecode } from "jwt-decode";

export const useAuthStore = create((set) => ({
  user: null,
  loading: false,
  role: null,

  login: async (email, password) => {
    set({ loading: true });
    try {
      const data = await api.post("/Auth", { email, password });
      localStorage.setItem("token", data.token);
      localStorage.setItem("refreshToken", data.refreshToken);
      const decoded = jwtDecode(data.token);
      set({ role: decoded.roles?.[0] || null });
      set({ user: data, loading: false });
    } catch (err) {
      set({ loading: false });
      throw err;
    }
  },

  register: async (userData) => {
    set({ loading: true });
    try {
      const data = await api.post("/Auth/register", userData);
      localStorage.setItem("token", data.token);
      localStorage.setItem("refreshToken", data.refreshToken);
      const decoded = jwtDecode(data.token);
      set({ role: decoded.roles?.[0] || null });
      set({ user: data, loading: false });
    } catch (err) {
      set({ loading: false });
      throw err;
    }
  },

  externalLogin: async () => {
    set({ loading: true });
    try {
      const data = await api.get("/Auth/externallogin");
      set({ loading: false });
      return data;
    } catch (err) {
      set({ loading: false });
      throw err;
    }
  },

  signIn: async () => {
    set({ loading: true });
    try {
      const data = await api.get("/Auth/signin");
      localStorage.setItem("token", data.token);
      localStorage.setItem("refreshToken", data.refreshToken);
      set({ user: data, loading: false });
    } catch (err) {
      set({ loading: false });
      throw err;
    }
  },

  logout: () => {
    localStorage.removeItem("token");
    localStorage.removeItem("refreshToken");
    set({ user: null });
  },
}));
