import SectionHeader from "./SectionHeader";
import { Sparkles } from "lucide-react";
import FeaturesTabs from "./FeaturesTabs";

const StatsSection = () => {
  const stats = [
    { value: "10,000+", label: "Happy Guests" },
    { value: "500+", label: "Premium Properties" },
    { value: "99%", label: "Satisfaction Rate" },
    { value: "24/7", label: "Hour Support" },
  ];

  return (
    <div
      id="stats-section"
      className="max-w-7xl mx-auto grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
    >
      {stats.map((stat, idx) => (
        <div
          key={idx}
          className="text-center p-6 rounded-2xl bg-gradient-to-br from-primary/5 to-transparent border border-primary/10 backdrop-blur-sm"
        >
          <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
            {stat.value}
          </div>
          <div className="text-sm text-muted-foreground font-medium">
            {stat.label}
          </div>
        </div>
      ))}
    </div>
  );
};

const ChooseHala = () => {
  return (
    <div>
      <SectionHeader
        badgeText="Hotel Features"
        heading="Why choose Hala?"
        description="We're reimagining how people live and travel, providing premium accommodations with the service and amenities you deserve"
        icon={Sparkles}
      />

      {/* Stats Section inside ChooseHala */}
      <StatsSection />

      <FeaturesTabs />
    </div>
  );
};

export default ChooseHala;
