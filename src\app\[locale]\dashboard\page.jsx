"use client";
import { loadUserFromStorage, logout } from "@/src/features/auth/authSlice";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const page = () => {
    const dispatch = useDispatch();
  const { user, loading,role } = useSelector((state) => state.auth);

  useEffect(() => {
    dispatch(loadUserFromStorage());
  }, [dispatch]);

  console.log("user:", user);
  return (
    <div className="pt-20">
      <h1>Dashboard</h1>
      <p>This is the dashboard page.</p>
      <h2 className="text-3xl font-bold underline ">Hello</h2>
      <div>
        <h2>{ user?.firstName }</h2>
        <h2>{ loading }</h2>
        <h2 className="text-3xl font-bold underline" >{ role }</h2>
        <button className=" cursor-pointer bg-primary p-3" onClick={() => dispatch(logout())} >Logout</button>
        <h2>are you here</h2>
      </div>
    </div>
  );
};

export default page;
