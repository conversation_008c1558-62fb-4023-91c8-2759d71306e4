"use client";

import { useRouter, usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";

const LanguageSwitcher = () => {
  const router = useRouter();
  const pathname = usePathname();

  const toggleLanguage = () => {
    // detect current locale
    const isArabic = pathname.startsWith("/ar");
    const newLocale = isArabic ? "en" : "ar";

    // remove locale prefix from current path
    const segments = pathname.split("/");
    if (segments[1] === "en" || segments[1] === "ar") {
      segments.splice(1, 1); // remove current locale
    }
    const cleanPath = segments.join("/") || "/";

    // redirect with new locale
    router.push(`/${newLocale}${cleanPath}`);
  };

  return (
    <Button variant="outline" className="cursor-pointer" onClick={toggleLanguage}>
      {pathname.startsWith("/ar") ? "En" : "Ar"}
    </Button>
  );
};

export default LanguageSwitcher;
