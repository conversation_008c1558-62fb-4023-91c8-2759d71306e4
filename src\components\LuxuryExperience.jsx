import { MapP<PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

const LuxuryExperience = () => {
  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative mb-5">
      <div className="rounded-lg bg-card text-card-foreground shadow-sm border-0 shadow-elevated bg-gradient-to-br from-background via-background to-brand-warm/20 overflow-hidden">
        <div className="p-12 md:p-16 text-center relative">
          {/* Top Gradient Bar */}
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-primary via-brand-ocean to-primary"></div>

          {/* Decorative Icons */}
          <div className="absolute top-8 right-8 opacity-20">
            <MapPin className="w-16 h-16 text-primary" />
          </div>
          <div className="absolute bottom-8 left-8 opacity-20">
            <Users className="w-12 h-12 text-primary" />
          </div>

          {/* Badge */}
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center bg-primary/10 rounded-full px-6 py-3 mb-8">
              <Sparkles className="w-5 h-5 text-primary mr-2" />
              <span className="text-primary font-semibold">
                Ready to Start Your Journey?
              </span>
            </div>

            {/* Heading */}
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Experience Luxury Living <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary via-brand-ocean to-primary">
                Around the World
              </span>
            </h2>

            {/* Subtext */}
            <p className="text-xl text-muted-foreground mb-12 leading-relaxed max-w-3xl mx-auto">
              Join thousands of satisfied guests who have discovered the perfect
              blend of comfort, luxury, and authentic local experiences with
              Hala.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  50K+
                </div>
                <div className="text-sm text-muted-foreground">
                  Happy Guests
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  200+
                </div>
                <div className="text-sm text-muted-foreground">
                  Premium Properties
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  4.9★
                </div>
                <div className="text-sm text-muted-foreground">
                  Average Rating
                </div>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap bg-primary hover:bg-primary/90 h-11 rounded-md bg-gradient-ocean hover:opacity-90 text-white shadow-soft hover:shadow-elevated transition-all duration-300 hover:scale-105 font-semibold px-8 py-6 text-lg">
                <Sparkles className="w-5 h-5 mr-2" />
                Explore Units
                <ArrowRight className="w-5 h-5 ml-2" />
              </button>

              <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap bg-background h-11 rounded-md border-2 border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300 font-semibold px-8 py-6 text-lg">
                Learn More
              </button>
            </div>

            {/* Footer Info */}
            <div className="mt-12 pt-8 border-t border-border/50">
              <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>Trusted by travelers worldwide</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>24/7 customer support</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span>Global destinations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LuxuryExperience;
