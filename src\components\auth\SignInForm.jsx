"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Mail, Lock, Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { useAuthStore } from "@/src/store/auth";

const signInSchema = z.object({
  email: z.string().min(1, "Email is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export default function SignInForm({ setAuthMode, onClose }) {
  const t = useTranslations("signin");
  const router = useRouter();

  const { login, user, token, isLoading, isError } = useAuthStore();

  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    if (token && user) {
      router.push("/dashboard");
    }
  }, [token, user, router]);

  const onSubmit = (data) => {
    login(data.email, data.password);
    reset();
    onClose();
    router.push("/dashboard");
  };

  return (
    <div className="relative w-full max-w-md bg-white rounded-lg border-0 max-h-[90vh] overflow-y-auto my-auto">
      <div className="px-6 pb-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Email */}
          <div className="flex flex-col gap-2">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              {t("email")}
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register("email")}
                type="email"
                placeholder={t("emailPlaceholder")}
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white pl-10 pr-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>

          {/* Password */}
          <div className="flex flex-col gap-2">
            <label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              {t("password")}
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register("password")}
                type={showPassword ? "text" : "password"}
                placeholder="••••••••"
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white pl-10 pr-10 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{errors.password.message}</p>
            )}
          </div>

          {/* isError */}
          {isError && (
            <p className="text-red-500 text-center text-sm">{isError}</p>
          )}

          {/* Submit */}
          <button
            type="submit"
            disabled={isLoading === "loading"}
            className="inline-flex items-center justify-center cursor-pointer w-full rounded-md bg-[#279fc7] hover:bg-[#279fc7]/90 px-4 py-2 text-sm font-medium text-white transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {isLoading === "loading" ? t("loading") : t("submit")}
          </button>

          {/* Switch to sign up */}
          <div className="text-center mt-4 text-sm text-gray-600">
            {t("dontHaveAccount")}{" "}
            <button
              type="button"
              onClick={() => setAuthMode("signup")}
              className="text-primary cursor-pointer hover:underline"
            >
              {t("signUp")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
