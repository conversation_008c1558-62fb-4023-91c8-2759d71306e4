"use client";

import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { Mail, Lock, Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";

import { login } from "../../features/auth/authSlice";

export default function SignInForm({ setAuthMode, onClose }) {
  const t = useTranslations("signin");
  const dispatch = useDispatch();
  const router = useRouter();

  // Redux state
  const { user, isLoading, isError, token } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (token && user) {
      router.push("/dashboard");
    }
  }, [token, user, router]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(login(formData));
    setFormData({ email: "", password: "" });
    onClose();
    router.push("/dashboard");
  };

  return (
    <div className="relative w-full max-w-md bg-white rounded-lg border-0 max-h-[90vh] overflow-y-auto my-auto">
      <div className="px-6 pb-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email */}
          <div className=" flex flex-col gap-2">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              {t("email")}
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                id="email"
                type="email"
                placeholder={t("emailPlaceholder")}
                value={formData.email}
                onChange={handleChange}
                required
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white pl-10 pr-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Password */}
          <div className=" flex flex-col gap-2">
            <label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              {t("password")}
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="••••••••"
                value={formData.password}
                onChange={handleChange}
                required
                className="flex h-10 w-full rounded-md border border-gray-300 bg-white pl-10 pr-10 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          {/* isError */}
          {isError && (
            <p className="text-red-500 text-center text-sm">{isError}</p>
          )}

          {/* Submit */}
          <button
            type="submit"
            disabled={isLoading === "loading"}
            className="inline-flex items-center justify-center cursor-pointer w-full rounded-md bg-[#279fc7] hover:bg-[#279fc7]/90 px-4 py-2 text-sm font-medium text-white transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {isLoading === "loading" ? t("loading") : t("submit")}
          </button>

          {/* Switch to sign up */}
          <div className="text-center mt-4 text-sm text-gray-600">
            {t("dontHaveAccount")}{" "}
            <button
              type="button"
              onClick={() => setAuthMode("signup")}
              className="text-primary cursor-pointer hover:underline"
            >
              {t("signUp")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
