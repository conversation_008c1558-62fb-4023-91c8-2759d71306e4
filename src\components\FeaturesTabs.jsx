"use client"
import { useState } from "react"
import {
  Star,
  Smartphone,
  Headphones,
  Sparkles,
  CreditCard,
  Shield,
  ChartColumn,
  MapPin,
  MessageCircle,
  ArrowRight,
  CircleCheckBig,
} from "lucide-react"

const tabs = [
  { id: "all", label: "All Features", icon: Star },
  { id: "booking", label: "Booking", icon: Smartphone },
  { id: "support", label: "Support", icon: Headphones },
  { id: "amenities", label: "Amenities", icon: Sparkles },
  { id: "payment", label: "Payment", icon: CreditCard },
  { id: "policies", label: "Policies", icon: Shield },
  { id: "management", label: "Management", icon: ChartColumn },
  { id: "location", label: "Location", icon: MapPin },
]

const features = [
  {
    id: 0,
    title: "Quick & Seamless Booking",
    desc: "Book your complete unit electronically from anywhere",
    icon: Smartphone,
    iconGradient: "from-blue-500 to-blue-600",
    tags: ["Instant Confirmation", "Mobile Friendly"],
    category: "booking",
  },
  {
    id: 1,
    title: "Secure & Trusted Payment",
    desc: "Integration with certified payment gateways and protection of your data",
    icon: CreditCard,
    iconGradient: "from-green-500 to-green-600",
    tags: ["SSL Encryption", "Multiple Currencies"],
    category: "payment",
  },
  {
    id: 2,
    title: "WhatsApp Services",
    desc: "Request cleaning, maintenance, delivery or meals directly through WhatsApp easily",
    icon: MessageCircle,
    iconGradient: "from-emerald-500 to-emerald-600",
    tags: ["24/7 Availability", "Instant Response"],
    category: "amenities",
  },
  {
    id: 3,
    title: "24/7 Customer Support",
    desc: "Expert assistance available around the clock for all your needs",
    icon: Headphones,
    iconGradient: "from-violet-500 to-violet-600",
    tags: ["24/7 Support", "Expert Team"],
    category: "support",
  },
  {
    id: 4,
    title: "Flexible Cancellation",
    desc: "Free cancellation up to 24 hours before check-in",
    icon: Shield,
    iconGradient: "from-rose-500 to-rose-600",
    tags: ["No Hidden Fees", "Full Refunds"],
    category: "policies",
  },
]

const FeatureCard = ({ feature }) => {
  const { title, desc, icon: Icon, iconGradient, tags } = feature
  return (
    <div
      role="article"
      tabIndex={0}
      className="rounded-lg bg-card text-card-foreground group shadow-lg hover:shadow-2xl transition-all duration-700 bg-gradient-to-br from-background via-background/80 to-primary/5 hover:-translate-y-3 hover:scale-105 cursor-pointer backdrop-blur-sm border border-primary/10 hover:border-primary/30"
    >
      <div className="p-6 text-center relative overflow-hidden h-full flex flex-col">
        {/* icon */}
        <div className="relative mb-6">
          <div
            className={`relative w-16 h-16 bg-gradient-to-r ${iconGradient} rounded-2xl flex items-center justify-center mx-auto shadow-xl`}
          >
            <Icon className="h-8 w-8 text-white" />
          </div>
        </div>

        {/* title + desc */}
        <h3 className="text-lg font-bold text-foreground mb-3">{title}</h3>
        <p className="text-muted-foreground leading-relaxed text-sm mb-4 flex-grow">
          {desc}
        </p>

        {/* tags */}
        <div className="flex flex-wrap gap-1 justify-center mb-4">
          {tags.map((tag, i) => (
            <span
              key={i}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary border border-primary/20"
            >
              <CircleCheckBig className="w-3 h-3 mr-1" />
              {tag}
            </span>
          ))}
        </div>

        {/* learn more button */}
        <button className="inline-flex cursor-pointer items-center justify-center gap-2 text-sm font-medium text-primary hover:underline">
          Learn More
          <ArrowRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  )
}

const FeaturesTabs = () => {
  const [activeTab, setActiveTab] = useState("all")

  const filteredFeatures =
    activeTab === "all"
      ? features
      : features.filter((f) => f.category === activeTab)

  return (
    <div>
      {/* Tabs */}
      <div className="flex flex-wrap justify-center gap-4 mb-12" role="tablist">
        {tabs.map(({ id, label, icon: Icon }) => {
          const isActive = activeTab === id
          return (
            <button
              key={id}
              role="tab"
              aria-selected={isActive}
              aria-controls={`features-panel-${id}`}
              onClick={() => setActiveTab(id)}
              className={`inline-flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive
                  ? "bg-primary text-white"
                  : "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
              }`}
            >
              <Icon className="w-4 h-4" />
              {label}
            </button>
          )
        })}
      </div>

      {/* Feature Grid */}
      <div
        className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        role="tabpanel"
      >
        {filteredFeatures.map((feature) => (
          <FeatureCard key={feature.id} feature={feature} />
        ))}
      </div>
    </div>
  )
}

export default FeaturesTabs