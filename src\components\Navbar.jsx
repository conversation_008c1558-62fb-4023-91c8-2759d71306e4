"use client";
import { useState } from "react";
import {
  Menu,
  X,
  Home,
  Info,
  BookOpen,
  Settings,
  Briefcase,
  ChevronDown,
  ChevronUp,
  Building,
  MessageCircle,
  LogIn,
  UserRoundPlus,
} from "lucide-react";
import { ModeToggle } from "./mode-toggle";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import LanguageSwitcher from "./LanguageSwitcher";
import AuthModal from "./auth/AuthModal";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useSelector } from "react-redux";

/* ---------------------- Reusable Nav Item ---------------------- */
function NavItem({ to, icon: Icon, children, onClick }) {
  return (
    <Link
      href={to}
      onClick={onClick}
      className="flex items-center gap-2 px-2 py-1 hover:text-primary"
    >
      <Icon size={18} /> {children}
    </Link>
  );
}

/* ---------------------- Dropdown (Desktop) ---------------------- */
function Dropdown({ label, icon: Icon, items }) {
  const [open, setOpen] = useState(false);

  return (
    <div
      className="relative mt-1"
      onMouseEnter={() => setOpen(true)}
      onMouseLeave={() => setOpen(false)}
    >
      <button
        onClick={() => setOpen(!open)}
        className="flex items-center gap-1 hover:text-primary"
      >
        <Icon size={18} /> {label}
        {open ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
      </button>

      <AnimatePresence>
        {open && (
          <motion.div
            initial={{ opacity: 0, y: -8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            transition={{ duration: 0.2 }}
            className="absolute left-0 mt-1 flex flex-col bg-white dark:bg-gray-800 shadow-lg rounded-md p-2 w-44 z-50"
          >
            {items.map((item) => (
              <NavItem key={item.to} to={item.to} icon={item.icon}>
                {item.label}
              </NavItem>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

/* ---------------------- Sidebar (Mobile) ---------------------- */
function Sidebar({
  isOpen,
  onClose,
  aboutOpen,
  setAboutOpen,
  navItems,
  t,
  user,
  onAuthOpen,
}) {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.4 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black z-40"
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: "-100%" }}
            animate={{ x: 0 }}
            exit={{ x: "-100%" }}
            transition={{ duration: 0.3 }}
            className="fixed top-0 left-0 h-full w-72 bg-white dark:bg-gray-900 shadow-lg z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-3 border-b dark:border-gray-700">
              <div className="text-xl font-bold text-primary">{t("hala")}</div>
              <button
                onClick={onClose}
                className="text-gray-700 dark:text-gray-200"
              >
                <X size={26} />
              </button>
            </div>

            {/* Nav Items */}
            <div className="flex flex-col mt-4 space-y-4 px-4">
              <NavItem to="/" icon={Home} onClick={onClose}>
                {t("home")}
              </NavItem>

              <NavItem to="/services" icon={Building} onClick={onClose}>
                {t("corporateStay")}
              </NavItem>

              <NavItem to="/contact" icon={MessageCircle} onClick={onClose}>
                {t("contact")}
              </NavItem>

              {/* About Expandable */}
              <button
                onClick={() => setAboutOpen(!aboutOpen)}
                className="flex items-center justify-between ml-2 hover:text-primary"
              >
                <span className="flex items-center gap-2">
                  <Info size={18} /> {t("about.about")}
                </span>
                {aboutOpen ? (
                  <ChevronUp size={18} />
                ) : (
                  <ChevronDown size={18} />
                )}
              </button>
              <AnimatePresence>
                {aboutOpen && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="ml-6 flex flex-col gap-2 overflow-hidden"
                  >
                    {navItems.about.map((item) => (
                      <NavItem
                        key={item.to}
                        to={item.to}
                        icon={item.icon}
                        onClick={onClose}
                      >
                        {item.label}
                      </NavItem>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Toggles + Auth */}
            <div className={`mt-6 px-4 flex flex-col space-y-3`}>
              <div className="flex items-center justify-around">
                <ModeToggle />
                <LanguageSwitcher />
              </div>
              {user ? (
                <div className="flex items-center gap-2">
                  <span className=" border-2 border-primary cursor-pointer text-primary font-extrabold px-3 py-2 rounded-full">
                    {" "}
                    {user?.firstName.slice(0, 2)}{" "}
                  </span>
                  <span className=" font-bold text-primary">{user?.firstName}</span>
                  <span className=" font-bold text-primary">{user?.lastName}</span>
                </div>
              ) : (
                <div className=" w-full flex flex-col gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      onAuthOpen("signin");
                      onClose();
                    }}
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <LogIn size={18} /> {t("signIn")}
                  </Button>
                  <Button
                    className="bg-primary cursor-pointer text-white hover:bg-blue-700 flex items-center gap-2"
                    onClick={() => {
                      onAuthOpen("signup");
                      onClose();
                    }}
                  >
                    <UserRoundPlus size={18} /> {t("signUp")}
                  </Button>
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

/* ---------------------- Main Navbar ---------------------- */
export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [aboutOpen, setAboutOpen] = useState(false);
  const [authOpen, setAuthOpen] = useState(false);
  const [authMode, setAuthMode] = useState("signin");
  const { user, loading } = useSelector((state) => state.auth);

  const t = useTranslations("nav");

  const navItems = {
    about: [
      { to: "/about/story", label: t("about.ourStory"), icon: BookOpen },
      {
        to: "/about/how-it-works",
        label: t("about.howItWorks"),
        icon: Settings,
      },
      { to: "/about/careers", label: t("about.careers"), icon: Briefcase },
    ],
  };

  return (
    <>
      <nav className="w-full bg-white dark:bg-gray-900 shadow-md fixed z-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between px-4 py-3">
            {/* Logo */}
            <Link
              href="/"
              onClick={() => setIsOpen(false)}
              className="flex items-center space-x-2"
            >
              <div className="w-12 h-12">
                <img src="/img/logo.webp" alt="Our logo" />
              </div>
              <div className="text-2xl font-bold text-primary">{t("hala")}</div>
            </Link>

            {/* Desktop Nav */}
            <div className="hidden md:flex flex-1 justify-center space-x-8">
              <NavItem to="/" icon={Home}>
                {t("home")}
              </NavItem>
              <NavItem to="/services" icon={Building}>
                {t("corporateStay")}
              </NavItem>
              <NavItem to="/contact" icon={MessageCircle}>
                {t("contact")}
              </NavItem>
              <Dropdown
                label={t("about.about")}
                icon={Info}
                items={navItems.about}
              />
            </div>

            {/* Desktop End Items */}
            <div className="hidden md:flex items-center space-x-3">
              <ModeToggle />
              <LanguageSwitcher />
              {user ? (
                <>
                  <div className=" border-2 border-primary cursor-pointer text-primary font-extrabold px-3 py-2 rounded-full">
                    {" "}
                    {user?.firstName.slice(0, 2)}{" "}
                  </div>
                </>
              ) : (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setAuthMode("signin");
                      setAuthOpen(true);
                    }}
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <LogIn size={18} /> {t("signIn")}
                  </Button>
                  <Button
                    onClick={() => {
                      setAuthMode("signup");
                      setAuthOpen(true);
                    }}
                    className="flex cursor-pointer items-center gap-2"
                  >
                    <UserRoundPlus size={18} /> {t("signUp")}
                  </Button>
                </div>
              )}
            </div>

            {/* Mobile Hamburger */}
            <button
              className="md:hidden text-gray-700 dark:text-gray-200"
              onClick={() => setIsOpen(true)}
            >
              <Menu size={28} />
            </button>
          </div>

          {/* Mobile Sidebar */}
          <Sidebar
            isOpen={isOpen}
            onClose={() => setIsOpen(false)}
            aboutOpen={aboutOpen}
            setAboutOpen={setAboutOpen}
            navItems={navItems}
            t={t}
            user={user}
            onAuthOpen={(mode) => {
              setAuthMode(mode);
              setAuthOpen(true);
            }}
          />
        </div>
      </nav>

      {/* Auth Modal */}
      <AuthModal
        open={authOpen}
        onClose={() => setAuthOpen(false)}
        setAuthMode={setAuthMode}
        authMode={authMode}
        initialMode={authMode}
      />
    </>
  );
}
