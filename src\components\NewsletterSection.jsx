"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Mail, Send, Gift, Star, Users, Zap } from "lucide-react"

const NewsletterSection = () => {
  const [email, setEmail] = useState("")
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (email) {
      setIsSubscribed(true)
      setEmail("")
    }
  }

  const benefits = [
    {
      icon: Gift,
      title: "Exclusive Deals",
      description: "Get access to members-only offers and discounts.",
    },
    {
      icon: Star,
      title: "Premium Content",
      description: "Receive curated guides and luxury travel insights.",
    },
    {
      icon: Zap,
      title: "Be the First to Know",
      description: "Stay updated with our latest properties and services.",
    },
  ]

  if (isSubscribed) {
    return (
      <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-brand-warm/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-12 border border-green-200">
            <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Subscription Successful!
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              Thank you for subscribing. You’ll now receive our latest news,
              deals, and exclusive offers directly to your inbox.
            </p>
            <div className="flex items-center justify-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>10,000+ Subscribers</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>Rated 5 Stars</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-brand-warm/20 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 opacity-10">
        <Mail className="w-24 h-24 text-primary" />
      </div>
      <div className="absolute bottom-10 right-10 opacity-10">
        <Send className="w-20 h-20 text-primary" />
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Benefits */}
          <div>
            <div className="inline-flex items-center bg-gradient-to-r from-primary/15 via-primary/10 to-brand-ocean/15 border border-primary/20 rounded-full px-6 py-3 mb-6 shadow-soft hover:shadow-elevated transition-all duration-500 hover:scale-105 group backdrop-blur-sm relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-brand-ocean/5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 rounded-full"></div>
              <div className="relative z-10 w-5 h-5 rounded-full bg-primary flex items-center justify-center mr-3 group-hover:rotate-12 transition-transform duration-300">
                <Mail className="w-3 h-3 text-white" />
              </div>
              <span className="relative z-10 text-primary text-sm font-bold tracking-wide group-hover:text-zinc-800 transition-colors duration-300">
                Stay Connected
              </span>
              <div className="relative z-10 w-2 h-2 bg-primary rounded-full ml-2 animate-pulse">
                <div className="absolute inset-0 bg-primary rounded-full animate-ping opacity-75"></div>
              </div>
            </div>

            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Subscribe to our newsletter and never miss out on the best luxury
              travel offers, tips, and updates.
            </p>

            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="flex items-start p-4 rounded-xl bg-background/50 hover:bg-background/80 transition-colors duration-300 space-x-4"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                    <benefit.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-1">
                      {benefit.title}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-center mt-8 text-sm text-muted-foreground space-x-6">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>10,000+ Subscribers</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>Rated 5 Stars</span>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                No Spam
              </Badge>
            </div>
          </div>

          {/* Right side - Newsletter form */}
          <div>
            <Card className="border-0 shadow-elevated bg-gradient-to-br from-background to-brand-warm/30 overflow-hidden">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Mail className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-2">
                    Join Our Newsletter
                  </h3>
                  <p className="text-muted-foreground">
                    Stay updated with our latest offers, news, and exclusive
                    deals.
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="h-12 border-border focus:border-primary transition-colors pr-12 focus:outline-none"
                      required
                    />
                    <Mail className="absolute top-1/2 right-4 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 bg-primary hover:opacity-90 text-white shadow-soft hover:shadow-elevated transition-all duration-300 font-semibold"
                  >
                    <Send className="w-5 h-5 mr-2" />
                    Subscribe
                  </Button>
                </form>

                <div className="text-center mt-6">
                  <p className="text-xs text-muted-foreground">
                    We respect your privacy. You can unsubscribe at any time.
                  </p>
                </div>

                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary/20 to-transparent rounded-full"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-tr from-brand-ocean/20 to-transparent rounded-full"></div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}

export default NewsletterSection
