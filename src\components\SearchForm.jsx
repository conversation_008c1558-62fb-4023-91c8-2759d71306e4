"use client";
import { useState } from "react";
import { MapPin, Calendar, Users, Search, X } from "lucide-react";
import { format } from "date-fns";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function SearchForm() {
  const [city, setCity] = useState("");
  const [checkIn, setCheckIn] = useState(null);
  const [checkOut, setCheckOut] = useState(null);
  const [guests, setGuests] = useState(1);

  const today = new Date();

  const handleSearch = () => {
    // Handle search logic will be here soon!💚
  };

  return (
    <div className="p-4 rounded-lg sm:p-6 lg:p-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 p-10 bg-white rounded-lg shadow-md">
        {/* CITY */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">CITY</label>
          <Select onValueChange={setCity}>
            <SelectTrigger className="w-full px-4" style={{ height: "48px" }}>
              <MapPin className="h-4 w-4 mr-2 text-gray-500" />
              <SelectValue placeholder="Select city" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cairo">Cairo</SelectItem>
              <SelectItem value="alexandria">Alexandria</SelectItem>
              <SelectItem value="giza">Giza</SelectItem>
              <SelectItem value="luxor">Luxor</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* CHECK-IN */}
        <div className="space-y-2 relative">
          <label className="text-sm font-medium text-gray-700">CHECK-IN</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full h-12 justify-between text-left font-normal pr-10"
              >
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  {checkIn ? format(checkIn, "PPP") : "Select date"}
                </div>
              </Button>
            </PopoverTrigger>

            {/* X button absolute */}
            {checkIn && (
              <button
                type="button"
                className="absolute right-3 top-[50%] text-gray-400 hover:text-primary"
                onClick={() => setCheckIn(null)}
              >
                <X className="h-4 w-4" />
              </button>
            )}

            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={checkIn}
                onSelect={(date) => {
                  setCheckIn(date);
                  if (checkOut && date >= checkOut) {
                    setCheckOut(null); // clear checkout if invalid
                  }
                }}
                disabled={(date) => date < today}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* CHECK-OUT */}
        <div className="space-y-2 relative">
          <label className="text-sm font-medium text-gray-700">CHECK-OUT</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full h-12 justify-between text-left font-normal pr-10"
              >
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  {checkOut ? format(checkOut, "PPP") : "Select date"}
                </div>
              </Button>
            </PopoverTrigger>

            {/* X button absolute */}
            {checkOut && (
              <button
                type="button"
                className="absolute right-3 top-[50%] text-gray-400 hover:text-primary"
                onClick={() => setCheckOut(null)}
              >
                <X className="h-4 w-4" />
              </button>
            )}

            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={checkOut}
                onSelect={(date) => {
                  if (!checkIn || date > checkIn) {
                    setCheckOut(date);
                  }
                }}
                disabled={(date) =>
                  date < today || (checkIn && date <= checkIn)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* GUESTS */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">GUESTS</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full h-12 justify-start text-left font-normal"
              >
                <Users className="h-4 w-4 mr-2" />
                {guests} guest{guests > 1 ? "s" : ""}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-52">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Guests</span>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setGuests(Math.max(1, guests - 1))}
                  >
                    -
                  </Button>
                  <span>{guests}</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setGuests(guests + 1)}
                  >
                    +
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* SEARCH BUTTON */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 opacity-0">
            &nbsp;
          </label>
          <Button
            className="w-full h-12 bg-primary cursor-pointer text-white font-medium rounded-lg"
            onClick={handleSearch}
          >
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>
    </div>
  );
}