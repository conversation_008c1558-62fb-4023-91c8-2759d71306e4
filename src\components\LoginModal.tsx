import { useState, useEffect } from "react";
// import { useTranslation } from "react-i18next";
// import { useAuth } from "@/contexts/AuthContext";
// import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

import { Eye, EyeOff, Mail, Lock, User, X, Phone } from "lucide-react";
import logoImage from "../../public/img/logo.webp";
import { useTranslations } from "next-intl";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: "login" | "register";
}

export  function LoginModal ({
  isOpen,
  onClose,
  defaultTab = "login",
}: LoginModalProps)  {
  const t =  useTranslations();
  // const { login, register, isLoading, isAuthenticated } = useAuth();
  // const { isRTL } = useLanguage();
  const isRTL = false;
  const isAuthenticated = false
  const isLoading = false

  const [activeTab, setActiveTab] = useState(defaultTab);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Login form state
  const [loginForm, setLoginForm] = useState({
    email: "",
    password: "",
  });

  // Register form state
  const [registerForm, setRegisterForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
  });

  // Close modal when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      onClose();
    }
  }, [isAuthenticated, onClose]);

  // Reset forms when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setActiveTab(defaultTab);
      setError("");
      setSuccess("");
      setLoginForm({ email: "", password: "" });
      setRegisterForm({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        password: "",
        confirmPassword: "",
      });
    }
  }, [isOpen, defaultTab]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!loginForm.email || !loginForm.password) {
      setError(t("auth.fillAllFields"));
      return;
    }

    const result = await login(loginForm.email, loginForm.password);

    if (result.success) {
      setSuccess(t("auth.loginSuccessful"));
      setTimeout(() => onClose(), 1500);
    } else {
      setError(result.error || t("auth.loginFailed"));
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (
      !registerForm.firstName ||
      !registerForm.lastName ||
      !registerForm.email ||
      !registerForm.phoneNumber ||
      !registerForm.password ||
      !registerForm.confirmPassword
    ) {
      setError(t("auth.fillAllFields"));
      return;
    }

    if (registerForm.password !== registerForm.confirmPassword) {
      setError(t("auth.passwordsDontMatch"));
      return;
    }

    if (registerForm.password.length < 6) {
      setError(t("auth.passwordTooShort"));
      return;
    }

    const result = await register(
      registerForm.email,
      registerForm.password,
      registerForm.firstName,
      registerForm.lastName,
      registerForm.phoneNumber,
      "", // dateOfBirth - empty since modal doesn't have this field
      "", // nationality - empty since modal doesn't have this field
      [] // identityDocuments - empty since modal doesn't have this field
    );

    if (result.success) {
      setSuccess(t("auth.registrationSuccessful"));
      setTimeout(() => onClose(), 1500);
    } else {
      setError(result.error || t("auth.registrationFailed"));
    }
  };

  const handleInputChange = (
    form: "login" | "register",
    field: string,
    value: string | File[]
  ) => {
    if (form === "login") {
      setLoginForm((prev) => ({ ...prev, [field]: value as string }));
    } else {
      setRegisterForm((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      style={{ minHeight: "100vh" }}
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-2xl border-0 max-h-[90vh] overflow-y-auto my-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 left-4 p-2 hover:bg-gray-100 rounded-lg transition-colors z-10"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>

        {/* Header */}
        <div className="text-center pt-8 pb-6 px-6">
          <div className="flex justify-center mb-4">
            <img
              src={logoImage}
              alt="Hala Logo"
              className="w-12 h-12 object-contain"
            />
          </div>
          <h2 className="text-xl font-bold text-gray-900">
            {activeTab === "login" ? t("auth.signIn") : t("auth.signUp")}
          </h2>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          {/* Error/Success Messages */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {success && (
            <Alert className="mb-4 border-green-200 bg-green-50">
              <AlertDescription className="text-green-800">
                {success}
              </AlertDescription>
            </Alert>
          )}

          {/* Tab Buttons */}
          <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveTab("login")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "login"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t("auth.signIn")}
            </button>
            <button
              onClick={() => setActiveTab("register")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === "register"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {t("auth.signUp")}
            </button>
          </div>

          {/* Login Form */}
          {activeTab === "login" && (
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="modal-login-email">{t("auth.email")}</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="modal-login-email"
                    type="email"
                    placeholder={t("auth.enterEmail")}
                    value={loginForm.email}
                    onChange={(e) =>
                      handleInputChange("login", "email", e.target.value)
                    }
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modal-login-password">
                  {t("auth.password")}
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="modal-login-password"
                    type={showPassword ? "text" : "password"}
                    placeholder={t("auth.enterPassword")}
                    value={loginForm.password}
                    onChange={(e) =>
                      handleInputChange("login", "password", e.target.value)
                    }
                    className="pl-10 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="modal-remember"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <Label
                    htmlFor="modal-remember"
                    className="text-sm text-gray-600"
                  >
                    {t("auth.rememberMe")}
                  </Label>
                </div>
                <Button
                  variant="link"
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {t("auth.forgotPassword")}
                </Button>
              </div>

              <Button
                type="submit"
                className="w-full bg-[#279fc7] hover:bg-[#279fc7]/90 text-white"
                disabled={isLoading}
              >
                {isLoading ? t("auth.signingIn") : t("auth.signIn")}
              </Button>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  {t("auth.demoCredentials")}
                </p>
              </div>

              {/* Social Login */}
              <div className="space-y-3">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">
                      {isRTL ? "أو" : "Or"}
                    </span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full border-gray-300 hover:bg-gray-50"
                >
                  <svg
                    className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"}`}
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  {isRTL ? "تسجيل الدخول مع Google" : "Login with Google"}
                </Button>
              </div>
            </form>
          )}

          {/* Register Form */}
          {activeTab === "register" && (
            <form onSubmit={handleRegister} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="modal-register-firstName">
                    {t("auth.firstName")}
                  </Label>
                  <div className="relative">
                    <User
                      className={`absolute ${
                        isRTL ? "right-3" : "left-3"
                      } top-3 h-4 w-4 text-gray-400`}
                    />
                    <Input
                      id="modal-register-firstName"
                      type="text"
                      placeholder={t("auth.enterFirstName")}
                      value={registerForm.firstName}
                      onChange={(e) =>
                        handleInputChange(
                          "register",
                          "firstName",
                          e.target.value
                        )
                      }
                      className={isRTL ? "pr-10" : "pl-10"}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="modal-register-lastName">
                    {t("auth.lastName")}
                  </Label>
                  <div className="relative">
                    <User
                      className={`absolute ${
                        isRTL ? "right-3" : "left-3"
                      } top-3 h-4 w-4 text-gray-400`}
                    />
                    <Input
                      id="modal-register-lastName"
                      type="text"
                      placeholder={t("auth.enterLastName")}
                      value={registerForm.lastName}
                      onChange={(e) =>
                        handleInputChange(
                          "register",
                          "lastName",
                          e.target.value
                        )
                      }
                      className={isRTL ? "pr-10" : "pl-10"}
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modal-register-email">{t("auth.email")}</Label>
                <div className="relative">
                  <Mail
                    className={`absolute ${
                      isRTL ? "right-3" : "left-3"
                    } top-3 h-4 w-4 text-gray-400`}
                  />
                  <Input
                    id="modal-register-email"
                    type="email"
                    placeholder={t("auth.enterEmail")}
                    value={registerForm.email}
                    onChange={(e) =>
                      handleInputChange("register", "email", e.target.value)
                    }
                    className={isRTL ? "pr-10" : "pl-10"}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modal-register-phoneNumber">
                  {t("auth.phoneNumber")}
                </Label>
                <div className="relative">
                  <Phone
                    className={`absolute ${
                      isRTL ? "right-3" : "left-3"
                    } top-3 h-4 w-4 text-gray-400`}
                  />
                  <Input
                    id="modal-register-phoneNumber"
                    type="tel"
                    placeholder={t("auth.enterPhoneNumber")}
                    value={registerForm.phoneNumber}
                    onChange={(e) =>
                      handleInputChange(
                        "register",
                        "phoneNumber",
                        e.target.value
                      )
                    }
                    className={isRTL ? "pr-10" : "pl-10"}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modal-register-password">
                  {t("auth.password")}
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="modal-register-password"
                    type={showPassword ? "text" : "password"}
                    placeholder={t("auth.createPassword")}
                    value={registerForm.password}
                    onChange={(e) =>
                      handleInputChange("register", "password", e.target.value)
                    }
                    className="pl-10 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modal-register-confirm-password">
                  {t("auth.confirmPassword")}
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="modal-register-confirm-password"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder={t("auth.confirmYourPassword")}
                    value={registerForm.confirmPassword}
                    onChange={(e) =>
                      handleInputChange(
                        "register",
                        "confirmPassword",
                        e.target.value
                      )
                    }
                    className="pl-10 pr-10"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="modal-terms"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  required
                />
                <Label htmlFor="modal-terms" className="text-sm text-gray-600">
                  {t("auth.agreeToTerms")}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
                  >
                    {t("auth.termsOfService")}
                  </Button>{" "}
                  {t("auth.and")}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
                  >
                    {t("auth.privacyPolicy")}
                  </Button>
                </Label>
              </div>

              <Button
                type="submit"
                className="w-full bg-[#279fc7] hover:bg-[#279fc7]/90 text-white"
                disabled={isLoading}
              >
                {isLoading
                  ? t("auth.creatingAccount")
                  : t("auth.createAccount")}
              </Button>

              {/* Social Login */}
              <div className="space-y-3">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">
                      {isRTL ? "أو" : "Or"}
                    </span>
                  </div>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full border-gray-300 hover:bg-gray-50"
                >
                  <svg
                    className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"}`}
                    viewBox="0 0 24 24"
                  >
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  {isRTL ? "تسجيل الدخول مع Google" : "Login with Google"}
                </Button>
              </div>
            </form>
          )}

          {/* Separator */}
          <div className="my-6">
            <Separator />
            <div className="text-center mt-4">
              <span className="text-sm text-gray-500">
                {t("auth.byContinuing")}{" "}
              </span>
              <Button
                variant="link"
                className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
              >
                {t("auth.termsOfService")}
              </Button>{" "}
              <span className="text-sm text-gray-500">{t("auth.and")} </span>
              <Button
                variant="link"
                className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
              >
                {t("auth.privacyPolicy")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;