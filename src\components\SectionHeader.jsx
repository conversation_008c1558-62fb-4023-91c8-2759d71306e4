export default function SectionHeader({
  badgeText = "Why Choose Hala",
  heading = "Why choose Hal<PERSON>?",
  description = "We're reimagining how people live and travel, providing premium accommodations with the service and amenities you deserve",
  icon: Icon, // single icon
  spin = false, // optional spinning effect
}) {
  return (
    <div className="text-center mb-20">
      {/* Badge */}
      <div className="inline-flex items-center bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-primary/20">
        {Icon && (
          <Icon
            className={`w-4 h-4 text-primary mr-2 ${spin ? "animate-spin-slow" : ""}`}
          />
        )}
        <span className="text-primary text-sm font-semibold tracking-wide">
          {badgeText}
        </span>
      </div>

      {/* Heading */}
      <h2 className="text-3xl lg:text-5xl font-bold mb-6 leading-tight text-slate-800">
        {heading}
      </h2>

      {/* Description */}
      <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
        {description}
      </p>
    </div>
  )
}