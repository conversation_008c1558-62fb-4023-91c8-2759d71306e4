import { <PERSON>, Star, ArrowRight } from "lucide-react";
import <PERSON><PERSON>eader from "./SectionHeader"; 

export default function FeaturedUnits() {
  // Static units data in English
  const units = [
    {
      title: "Luxury Apartment in Center",
      img: "/img/heroImg.webp",
      price: "SAR 2,500/month",
      rating: 4.8,
      details: ["2 Bedroom", "2 Bathroom", "120 sqm"],
    },
    {
      title: "Modern Studio with Sea View",
      img: "/img/heroImg.webp",
      price: "SAR 1,800/month",
      rating: 4.6,
      details: ["1 Bedroom", "1 Bathroom", "80 sqm"],
    },
    {
      title: "Spacious Villa with Garden",
      img: "/img/heroImg.webp",
      price: "SAR 5,000/month",
      rating: 4.9,
      details: ["4 Bedroom", "3 Bathroom", "350 sqm"],
    },
  ];

  return (
    <div className="py-16 space-y-24">
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* ✅ Using SectionHeader */}
        <SectionHeader
          badgeText="Featured Units"
          heading="Featured Units for Rent"
          description="Choose from a diverse collection of fully furnished residential units."
          icon={House}
        />

        {/* Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {units.map((unit, i) => (
            <div
              key={i}
              className="rounded-lg bg-card text-card-foreground shadow-sm group overflow-hidden border-0 shadow-soft hover:shadow-elevated transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
              style={{ animationDelay: `${i * 100}ms` }}
            >
              <div className="relative overflow-hidden">
                <img
                  src={unit.img}
                  alt={unit.title}
                  className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

                {/* Rating */}
                <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-lg px-2 py-1">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-white text-sm font-semibold">
                      {unit.rating}
                    </span>
                  </div>
                </div>

                {/* Price */}
                <div className="absolute top-4 left-4">
                  <div className="inline-flex items-center rounded-full border text-xs font-bold px-3 py-2 bg-primary text-white">
                    {unit.price}
                  </div>
                </div>

                {/* Info */}
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h3 className="text-xl font-bold mb-2">{unit.title}</h3>
                  <div className="flex items-center space-x-4 text-sm">
                    {unit.details.map((d, idx) => (
                      <span key={idx}>{d}</span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Button */}
              <div className="p-6">
                <button className="inline-flex cursor-pointer items-center justify-between gap-2 w-full rounded-md text-sm font-semibold text-primary border border-primary/20 hover:border-primary hover:bg-accent hover:text-accent-foreground group-hover:bg-primary group-hover:text-white h-10 px-4 py-2 transition-all duration-300">
                  <span>View Details</span>
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
