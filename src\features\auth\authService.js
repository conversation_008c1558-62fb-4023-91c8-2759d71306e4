const API_URL = "https://hala-reservation.runasp.net/api/auth"

// login
const login = async (userData) => {
  const res = await fetch(`${API_URL}`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(userData),
  })
  if (!res.ok) throw new Error("Login failed")
  return res.json()
}

// register
const register = async (userData) => {
  const res = await fetch(`${API_URL}/register`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(userData),
  })
  if (!res.ok) throw new Error("Registration failed")
  return res.json()
}

// refresh
const refresh = async () => {
  const refreshToken = localStorage.getItem("refreshToken")
  const res = await fetch(`${API_URL}/refresh-token`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ refreshToken }),
  })
  if (!res.ok) throw new Error("Refresh failed")
  return res.json()
}

export { login, register, refresh }
