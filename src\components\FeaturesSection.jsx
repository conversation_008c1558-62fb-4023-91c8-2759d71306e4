import { Home, Sparkles, <PERSON>, <PERSON>, <PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import SectionHeader from "./SectionHeader"; // ✅ import reusable header

const FeaturesSection = () => {
  const features = [
    {
      id: "luxury-lobby-1",
      title: "Luxury Lobby",
      image:
        "https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      icon: Home,
    },
    {
      id: "luxury-lobby-2",
      title: "Exclusive Experience",
      image:
        "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      icon: Sparkles,
    },
    {
      id: "luxury-lobby-3",
      title: "Romantic Atmosphere",
      image:
        "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      icon: Heart,
    },
    {
      id: "luxury-lobby-4",
      title: "Top Rated Service",
      image:
        "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      icon: Star,
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 md:py-16">
      {/* ✅ Reusable SectionHeader */}
      <SectionHeader
        badgeText="Hotel Features"
        heading="Explore Our Hotel"
        description="Everything you need for an unforgettable stay"
        icon={Sparkles}
      />

      {/* Grid of features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-4">
        {features.map((feature) => (
          <FeatureCard key={feature.id} feature={feature} />
        ))}
      </div>
    </div>
  );
};

// FeatureCard Component
const FeatureCard = ({ feature }) => {
  const IconComponent = feature.icon;

  return (
    <div className="group cursor-pointer overflow-hidden border-0 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform p-0">
      <div className="relative overflow-hidden">
        <div className="aspect-w-4 aspect-h-5">
          <img
            alt={feature.title}
            className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500"
            src={feature.image}
            loading="lazy"
          />
        </div>

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Content */}
        <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
          <div className="flex items-center gap-3 mb-3">
            <IconComponent className="w-6 h-6 text-white/80" />
            <h3 className="text-xl font-semibold text-white">
              {feature.title}
            </h3>
          </div>

          <div className="flex items-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-200">
            <button
              type="button"
              className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full px-4 py-2.5 text-sm font-medium text-white transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl group/button"
              aria-label={`Explore ${feature.title}`}
            >
              <Eye className="w-4 h-4 opacity-80" />
              <span>Explore More</span>
              <ArrowRight className="w-4 h-4 transform group-hover/button:translate-x-1 transition-transform duration-300" />
            </button>
            <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesSection;
