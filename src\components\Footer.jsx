import { Instagram, Twitter, Linkedin, Facebook } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-foreground text-background py-12 sm:py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Top Grid */}
        <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-3 mb-12">
          {/* Logo + About */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6 space-x-2">
              <div className="w-8 h-8 bg-gradient-ocean rounded-lg flex items-center justify-center">
                <span className="text-white p-2 rounded-md font-extrabold bg-primary text-sm">
                  H
                </span>
              </div>
              <span className="text-xl font-semibold">hala</span>
            </div>
            <p className="text-background/80 leading-relaxed mb-6 text-sm sm:text-base">
              Reimagining the future of living with premium accommodations in the
              world's most vibrant neighborhoods.
            </p>
            <div className="flex space-x-4">
              {[Instagram, Twitter, Linkedin, Facebook].map((Icon, idx) => (
                <button
                  key={idx}
                  className="inline-flex items-center justify-center h-10 w-10 rounded-md text-background/80 hover:text-primary hover:bg-background/10 transition-colors"
                >
                  <Icon className="h-5 w-5" />
                </button>
              ))}
            </div>
          </div>
          
          <div className="flex justify-between">
            {/* Company */}
            <div>
              <h3 className="text-lg font-semibold mb-6">Company</h3>
              <ul className="space-y-3 sm:space-y-4 text-sm sm:text-base">
                {["About Us", "Careers", "Press", "Blog", "Investor Relations"].map(
                  (item) => (
                    <li key={item}>
                      <a
                        href="#"
                        className="text-background/80 hover:text-background transition-colors"
                      >
                        {item}
                      </a>
                    </li>
                  )
                )}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-lg font-semibold mb-6">Support</h3>
              <ul className="space-y-3 sm:space-y-4 text-sm sm:text-base">
                <li>
                  <a
                    href="/faq"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Frequently Asked Questions
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Help Center
                  </a>
                </li>
                <li>
                  <a
                    href="/contact"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Contact Us
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Safety & Security
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Community Guidelines
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-background/80 hover:text-background transition-colors"
                  >
                    Accessibility
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Stay Updated</h3>
            <p className="text-background/80 mb-4 text-sm sm:text-base">
              Get the latest news and exclusive offers delivered to your inbox.
            </p>
            <form className="space-y-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex h-10 w-full rounded-md border border-background/20 bg-background/10 px-3 py-2 text-sm sm:text-base text-background placeholder:text-background/60 focus:bg-background/20 focus:outline-none focus:ring-2 focus:ring-ring"
              />
              <button type="submit" className="inline-flex cursor-pointer w-full items-center justify-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-brand-teal-dark focus:outline-none focus:ring-2 focus:ring-ring">
                Subscribe
              </button>
            </form>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 border-t border-background/20">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 text-center md:text-left">
            <p className="text-background/60 text-xs sm:text-sm">
              © 2024 Hala. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center md:justify-end gap-4 text-xs sm:text-sm">
              {["Privacy Policy", "Terms of Service", "Cookie Policy"].map(
                (item) => (
                  <a
                    key={item}
                    href="#"
                    className="text-background/60 hover:text-background transition-colors"
                  >
                    {item}
                  </a>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}